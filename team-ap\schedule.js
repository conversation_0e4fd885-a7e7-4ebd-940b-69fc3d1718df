// Configuration
const API_BASE_URL = 'http://localhost:8000'; // Change this to your deployed backend URL

// Global state
let currentTournament = null;
let teams = [];
let matches = [];
let currentFilter = 'all';

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadData();
});

// Setup event listeners
function setupEventListeners() {
    // Mobile menu toggle
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }
    
    // Filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            // Update active filter
            document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Update current filter and display matches
            currentFilter = btn.dataset.filter;
            displayMatches();
        });
    });
}

// API helper function
async function apiCall(endpoint) {
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        
        if (response.ok) {
            return await response.json();
        } else {
            console.error(`API call failed: ${response.status} ${response.statusText}`);
            return null;
        }
    } catch (error) {
        console.error('API call error:', error);
        return null;
    }
}

// Load all data
async function loadData() {
    await loadCurrentTournament();
    if (currentTournament) {
        await loadTeams();
        await loadMatches();
        displayTournamentInfo();
        displayMatches();
    } else {
        await loadMostRecentTournament();
    }
}

// Load current live tournament
async function loadCurrentTournament() {
    currentTournament = await apiCall('/api/tournaments/live');
}

// Load most recent completed tournament if no live tournament
async function loadMostRecentTournament() {
    const completedTournaments = await apiCall('/api/tournaments/completed');
    if (completedTournaments && completedTournaments.length > 0) {
        currentTournament = completedTournaments.sort((a, b) => 
            new Date(b.created_at) - new Date(a.created_at)
        )[0];
        
        await loadTeams();
        await loadMatches();
        displayTournamentInfo();
        displayMatches();
    } else {
        displayNoTournament();
    }
}

// Load teams for current tournament
async function loadTeams() {
    if (currentTournament) {
        teams = await apiCall(`/api/tournaments/${currentTournament.id}/teams`) || [];
    }
}

// Load matches for current tournament
async function loadMatches() {
    if (currentTournament) {
        matches = await apiCall(`/api/tournaments/${currentTournament.id}/matches`) || [];
        // Sort matches by start time
        matches.sort((a, b) => new Date(a.start_time) - new Date(b.start_time));
    }
}

// Display tournament information
function displayTournamentInfo() {
    const tournamentInfoElement = document.getElementById('tournamentInfo');
    
    if (!currentTournament) {
        displayNoTournament();
        return;
    }
    
    const statusText = currentTournament.status === 'live' ? 'LIVE NOW' : 
                      currentTournament.status === 'upcoming' ? 'UPCOMING' : 'COMPLETED';
    
    tournamentInfoElement.innerHTML = `
        <div class="tournament-info-content">
            <h2 class="tournament-name">${currentTournament.name}</h2>
            <div class="tournament-details">
                <span class="tournament-type">${currentTournament.type}</span>
                <span class="tournament-status ${currentTournament.status}">${statusText}</span>
                ${currentTournament.prize_pool ? `<span class="tournament-prize">₹${currentTournament.prize_pool.toLocaleString()}</span>` : ''}
            </div>
        </div>
    `;
}

// Helper function to get rank image path
function getRankImagePath(filename) {
    return filename ? `rank_png/${filename}` : '';
}

// Helper function to create player list with ranks
function createPlayerList(team, maxPlayers = 3) {
    if (!team) return '';

    let players = [];
    if (team.players && team.players.length > 0) {
        players = team.players;
    } else if (team.members && team.members.length > 0) {
        players = team.members.map(member => ({ name: member, rank: null }));
    }

    if (players.length === 0) return '';

    const displayPlayers = players.slice(0, maxPlayers);
    const remainingCount = players.length - maxPlayers;

    let playerList = displayPlayers.map(player => {
        const rankImg = player.rank ?
            `<img src="${getRankImagePath(player.rank)}" alt="Rank" class="rank-icon">` : '';
        return `${rankImg}${player.name}`;
    }).join(', ');

    if (remainingCount > 0) {
        playerList += ` +${remainingCount} more`;
    }

    return playerList;
}

// Display matches based on current filter
function displayMatches() {
    const matchesListElement = document.getElementById('matchesList');

    if (!matches || matches.length === 0) {
        matchesListElement.innerHTML = '<div class="no-data">No matches found for this tournament.</div>';
        return;
    }

    // Filter matches based on current filter
    let filteredMatches = matches;
    if (currentFilter !== 'all') {
        filteredMatches = matches.filter(match => match.status === currentFilter);
    }

    if (filteredMatches.length === 0) {
        matchesListElement.innerHTML = `<div class="no-data">No ${currentFilter} matches found.</div>`;
        return;
    }

    matchesListElement.innerHTML = filteredMatches.map(match => {
        const teamA = getTeamById(match.teamA);
        const teamB = getTeamById(match.teamB);
        const winner = match.winner ? getTeamById(match.winner) : null;

        return `
            <div class="schedule-match-card">
                <div class="match-header">
                    <div class="match-id">Match ${match.match_id || match.id}</div>
                    <div class="match-status ${match.status}">${match.status.toUpperCase()}</div>
                </div>

                <div class="match-teams">
                    <div class="team ${winner && winner.id === teamA?.id ? 'winner' : ''}">
                        <div class="team-info">
                            <div class="team-name">${teamA ? teamA.name : 'TBD'}</div>
                            <div class="team-initials">${teamA ? teamA.initials : '??'}</div>
                            ${teamA ? `<div class="team-players">${createPlayerList(teamA)}</div>` : ''}
                        </div>
                        ${match.status === 'completed' && match.scoreA !== null ?
                            `<div class="team-score">${match.scoreA}</div>` :
                            '<div class="team-score">-</div>'}
                    </div>

                    <div class="vs-section">
                        <div class="vs">VS</div>
                        <div class="match-time">${formatMatchTime(match.start_time)}</div>
                    </div>

                    <div class="team ${winner && winner.id === teamB?.id ? 'winner' : ''}">
                        <div class="team-info">
                            <div class="team-name">${teamB ? teamB.name : 'TBD'}</div>
                            <div class="team-initials">${teamB ? teamB.initials : '??'}</div>
                            ${teamB ? `<div class="team-players">${createPlayerList(teamB)}</div>` : ''}
                        </div>
                        ${match.status === 'completed' && match.scoreB !== null ?
                            `<div class="team-score">${match.scoreB}</div>` :
                            '<div class="team-score">-</div>'}
                    </div>
                </div>

                ${winner ? `
                    <div class="match-result">
                        <div class="winner-announcement">
                            🏆 Winner: <span class="winner-name">${winner.name}</span>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }).join('');
}

// Display no tournament message
function displayNoTournament() {
    const tournamentInfoElement = document.getElementById('tournamentInfo');
    const matchesListElement = document.getElementById('matchesList');
    
    const noTournamentMessage = `
        <div class="no-tournament">
            <h2>No Tournament Currently Available</h2>
            <p>Check back soon for upcoming tournaments!</p>
        </div>
    `;
    
    if (tournamentInfoElement) {
        tournamentInfoElement.innerHTML = noTournamentMessage;
    }
    
    if (matchesListElement) {
        matchesListElement.innerHTML = '<div class="no-data">No matches available.</div>';
    }
}

// Helper function to get team by ID
function getTeamById(teamId) {
    return teams.find(team => team.id === teamId);
}

// Format match time for display
function formatMatchTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = date - now;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMs < 0) {
        // Past match
        return date.toLocaleDateString('en-IN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } else if (diffHours < 24) {
        // Today
        return `Today ${date.toLocaleTimeString('en-IN', {
            hour: '2-digit',
            minute: '2-digit'
        })}`;
    } else if (diffDays < 7) {
        // This week
        return date.toLocaleDateString('en-IN', {
            weekday: 'short',
            hour: '2-digit',
            minute: '2-digit'
        });
    } else {
        // Future
        return date.toLocaleDateString('en-IN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}
