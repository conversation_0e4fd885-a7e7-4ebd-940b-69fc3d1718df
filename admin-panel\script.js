// Configuration
const API_BASE_URL = 'http://localhost:8000'; // Change this to your deployed backend URL

// Global state
let currentUser = null;
let authToken = null;
let tournaments = [];
let teams = [];
let matches = [];
let brackets = [];

// DOM elements
const loginScreen = document.getElementById('loginScreen');
const dashboardScreen = document.getElementById('dashboardScreen');
const loginForm = document.getElementById('loginForm');
const loginError = document.getElementById('loginError');
const welcomeUser = document.getElementById('welcomeUser');
const logoutBtn = document.getElementById('logoutBtn');
const modal = document.getElementById('modal');
const modalBody = document.getElementById('modalBody');
const closeModal = document.querySelector('.admin-modal-close');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    checkAuthStatus();
    setupEventListeners();
});

// Check if user is already logged in
function checkAuthStatus() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');
    const tokenExpiry = localStorage.getItem('tokenExpiry');
    
    if (token && user && tokenExpiry && new Date().getTime() < parseInt(tokenExpiry)) {
        authToken = token;
        currentUser = user;
        showDashboard();
    } else {
        showLogin();
    }
}

// Setup event listeners
function setupEventListeners() {
    // Login form
    loginForm.addEventListener('submit', handleLogin);
    
    // Logout button
    logoutBtn.addEventListener('click', handleLogout);
    
    // Tab switching
    document.querySelectorAll('.admin-tab-btn').forEach(btn => {
        btn.addEventListener('click', () => switchTab(btn.dataset.tab));
    });
    
    // Modal close
    closeModal.addEventListener('click', closeModalHandler);
    window.addEventListener('click', (e) => {
        if (e.target === modal) closeModalHandler();
    });
    
    // Add buttons
    document.getElementById('refreshTournamentsBtn').addEventListener('click', () => {
        console.log('Manual refresh triggered');
        loadTournaments();
    });
    document.getElementById('addTournamentBtn').addEventListener('click', () => showTournamentForm());
    document.getElementById('addTeamBtn').addEventListener('click', () => showTeamForm());
    document.getElementById('addMatchBtn').addEventListener('click', () => showMatchForm());
    document.getElementById('addBracketBtn').addEventListener('click', () => showBracketForm());
    
    // Tournament selects
    document.getElementById('teamTournamentSelect').addEventListener('change', loadTeams);
    document.getElementById('matchTournamentSelect').addEventListener('change', loadMatches);
    document.getElementById('bracketTournamentSelect').addEventListener('change', loadBrackets);
}

// Authentication functions
async function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        const response = await fetch(`${API_BASE_URL}/api/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password }),
        });

        if (response.ok) {
            const data = await response.json();
            authToken = data.access_token;
            currentUser = username;

            // Store in localStorage with expiry
            const expiryTime = new Date().getTime() + (data.expires_in * 1000);
            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', currentUser);
            localStorage.setItem('tokenExpiry', expiryTime.toString());

            showDashboard();
        } else {
            showError('Invalid username or password');
        }
    } catch (error) {
        console.error('Login error:', error);
        showError('Network error. Please check your connection.');
    }
}

function handleLogout() {
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    localStorage.removeItem('tokenExpiry');
    showLogin();
}

function showLogin() {
    loginScreen.classList.remove('hidden');
    dashboardScreen.classList.add('hidden');
    loginError.textContent = '';
    loginForm.reset();
}

function showDashboard() {
    loginScreen.classList.add('hidden');
    dashboardScreen.classList.remove('hidden');
    welcomeUser.textContent = `Welcome, ${currentUser}`;
    loadTournaments();
}

function showError(message) {
    loginError.textContent = message;
}

// Tab switching
function switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.admin-tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update tab content
    document.querySelectorAll('.admin-tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');
    
    // Load data for the active tab
    switch(tabName) {
        case 'tournaments':
            loadTournaments();
            break;
        case 'teams':
            loadTeams();
            break;
        case 'matches':
            loadMatches();
            break;
        case 'brackets':
            loadBrackets();
            break;
    }
}

// API helper function
async function apiCall(endpoint, method = 'GET', data = null) {
    console.log(`API Call: ${method} ${API_BASE_URL}${endpoint}`, data);

    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
        },
    };

    if (authToken) {
        options.headers['Authorization'] = `Bearer ${authToken}`;
    }

    if (data) {
        options.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, options);
        console.log(`API Response: ${response.status} ${response.statusText}`);

        if (response.status === 401) {
            console.error('Unauthorized - logging out');
            handleLogout();
            return null;
        }

        if (response.ok) {
            const result = await response.json();
            console.log('API Success:', result);
            return result;
        } else {
            const errorText = await response.text();
            console.error('API Error Response:', errorText);
            throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }
    } catch (error) {
        console.error('API call error:', error);
        alert(`Error: ${error.message}`);
        return null;
    }
}

// Tournament functions
async function loadTournaments() {
    console.log('Loading tournaments...');
    // Load all tournaments regardless of status for admin panel
    const allTournaments = await apiCall('/api/tournaments/all');

    console.log('All tournaments from API:', allTournaments);

    tournaments = allTournaments || [];

    console.log('Final tournaments array:', tournaments);

    displayTournaments();
    updateTournamentSelects();
}

function displayTournaments() {
    const container = document.getElementById('tournamentsList');
    
    if (tournaments.length === 0) {
        container.innerHTML = '<p>No tournaments found.</p>';
        return;
    }
    
    container.innerHTML = tournaments.map(tournament => `
        <div class="admin-data-card">
            <h3>${tournament.name}</h3>
            <p><strong>Type:</strong> ${tournament.type}</p>
            <p><strong>Prize Pool:</strong> ₹${tournament.prize_pool || 0}</p>
            <p><strong>Status:</strong> <span class="admin-status ${tournament.status}">${tournament.status.toUpperCase()}</span></p>
            ${tournament.start_date ? `<p><strong>Start Date:</strong> ${new Date(tournament.start_date).toLocaleDateString()}</p>` : ''}
            ${tournament.end_date ? `<p><strong>End Date:</strong> ${new Date(tournament.end_date).toLocaleDateString()}</p>` : ''}
            <div class="admin-card-actions">
                <button class="admin-btn admin-btn-primary" onclick="editTournament('${tournament.id}')">EDIT</button>
                <button class="admin-btn admin-btn-secondary" onclick="setTournamentStatus('${tournament.id}', 'live')">SET LIVE</button>
                <button class="admin-btn admin-btn-danger" onclick="setTournamentStatus('${tournament.id}', 'completed')">COMPLETE</button>
                <button class="admin-btn admin-btn-danger" onclick="deleteTournament('${tournament.id}')">DELETE</button>
            </div>
        </div>
    `).join('');
}

function updateTournamentSelects() {
    const selects = [
        document.getElementById('teamTournamentSelect'),
        document.getElementById('matchTournamentSelect'),
        document.getElementById('bracketTournamentSelect')
    ];
    
    selects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">Select Tournament</option>';
        
        tournaments.forEach(tournament => {
            const option = document.createElement('option');
            option.value = tournament.id;
            option.textContent = tournament.name;
            select.appendChild(option);
        });
        
        if (currentValue) select.value = currentValue;
    });
}

// Modal functions
function showModal(title, content) {
    document.getElementById('modalTitle').textContent = title;
    modalBody.innerHTML = content;
    modal.style.display = 'block';
}

function closeModalHandler() {
    modal.style.display = 'none';
}

// Tournament form
function showTournamentForm(tournament = null) {
    const isEdit = tournament !== null;
    const title = isEdit ? 'Edit Tournament' : 'Add Tournament';
    
    const content = `
        <form id="tournamentForm">
            <div class="form-group">
                <label for="tournamentName">Tournament Name:</label>
                <input type="text" id="tournamentName" value="${tournament?.name || ''}" required>
            </div>
            <div class="form-group">
                <label for="tournamentType">Type:</label>
                <select id="tournamentType" required>
                    <option value="Single Elimination" ${tournament?.type === 'Single Elimination' ? 'selected' : ''}>Single Elimination</option>
                    <option value="Double Elimination" ${tournament?.type === 'Double Elimination' ? 'selected' : ''}>Double Elimination</option>
                    <option value="Round Robin" ${tournament?.type === 'Round Robin' ? 'selected' : ''}>Round Robin</option>
                </select>
            </div>
            <div class="form-group">
                <label for="prizePool">Prize Pool (₹):</label>
                <input type="number" id="prizePool" value="${tournament?.prize_pool || 0}" min="0">
            </div>
            <div class="form-group">
                <label for="startDate">Start Date:</label>
                <input type="datetime-local" id="startDate" value="${tournament?.start_date ? new Date(tournament.start_date).toISOString().slice(0, 16) : ''}">
            </div>
            <div class="form-group">
                <label for="endDate">End Date:</label>
                <input type="datetime-local" id="endDate" value="${tournament?.end_date ? new Date(tournament.end_date).toISOString().slice(0, 16) : ''}">
            </div>
            <div class="form-actions">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeModalHandler()">CANCEL</button>
                <button type="submit" class="admin-btn admin-btn-primary">${isEdit ? 'UPDATE' : 'CREATE'}</button>
            </div>
        </form>
    `;
    
    showModal(title, content);
    
    document.getElementById('tournamentForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = {
            name: document.getElementById('tournamentName').value,
            type: document.getElementById('tournamentType').value,
            prize_pool: parseInt(document.getElementById('prizePool').value) || 0,
            start_date: document.getElementById('startDate').value || null,
            end_date: document.getElementById('endDate').value || null
        };
        
        const endpoint = isEdit ? `/api/tournaments/${tournament.id}` : '/api/tournaments';
        const method = isEdit ? 'PATCH' : 'POST';
        const result = await apiCall(endpoint, method, formData);

        if (result) {
            console.log(`Tournament ${isEdit ? 'updated' : 'created'} successfully:`, result);
            closeModalHandler();
            await loadTournaments();
            alert(`Tournament ${isEdit ? 'updated' : 'created'} successfully!`);
        } else {
            console.error(`Failed to ${isEdit ? 'update' : 'create'} tournament`);
            alert(`Failed to ${isEdit ? 'update' : 'create'} tournament. Check console for details.`);
        }
    });
}

async function setTournamentStatus(tournamentId, status) {
    const result = await apiCall(`/api/tournaments/${tournamentId}/status`, 'PATCH', { status });
    if (result) {
        loadTournaments();
    }
}

async function editTournament(tournamentId) {
    const tournament = tournaments.find(t => t.id === tournamentId);
    if (tournament) {
        showTournamentForm(tournament);
    }
}

async function deleteTournament(tournamentId) {
    const tournament = tournaments.find(t => t.id === tournamentId);
    if (!tournament) return;

    if (confirm(`Are you sure you want to delete the tournament "${tournament.name}"? This action cannot be undone and will also delete all associated teams, matches, and brackets.`)) {
        const result = await apiCall(`/api/tournaments/${tournamentId}`, 'DELETE');
        if (result) {
            alert('Tournament deleted successfully!');
            loadTournaments();
        } else {
            alert('Failed to delete tournament. Check console for details.');
        }
    }
}

// Team functions
async function loadTeams() {
    const tournamentId = document.getElementById('teamTournamentSelect').value;
    if (!tournamentId) {
        document.getElementById('teamsList').innerHTML = '<p>Please select a tournament.</p>';
        return;
    }

    teams = await apiCall(`/api/tournaments/${tournamentId}/teams`) || [];
    displayTeams();
}

function displayTeams() {
    const container = document.getElementById('teamsList');

    if (teams.length === 0) {
        container.innerHTML = '<p>No teams found for this tournament.</p>';
        return;
    }

    container.innerHTML = teams.map(team => `
        <div class="admin-data-card">
            <h3>${team.name} (${team.initials})</h3>
            <p><strong>Members:</strong> ${team.members.join(', ')}</p>
            ${team.icon ? `<p><strong>Icon:</strong> <img src="${team.icon}" alt="Team Icon" style="width: 32px; height: 32px; border-radius: 4px;"></p>` : ''}
            <div class="admin-card-actions">
                <button class="admin-btn admin-btn-primary" onclick="editTeam('${team.id}')">EDIT</button>
                <button class="admin-btn admin-btn-danger" onclick="deleteTeam('${team.id}')">DELETE</button>
            </div>
        </div>
    `).join('');
}

function showTeamForm(team = null) {
    const tournamentId = document.getElementById('teamTournamentSelect').value;
    if (!tournamentId && !team) {
        alert('Please select a tournament first.');
        return;
    }

    const isEdit = team !== null;
    const title = isEdit ? 'Edit Team' : 'Add Team';

    const content = `
        <form id="teamForm">
            <div class="form-group">
                <label for="teamName">Team Name:</label>
                <input type="text" id="teamName" value="${team?.name || ''}" required>
            </div>
            <div class="form-group">
                <label for="teamInitials">Initials:</label>
                <input type="text" id="teamInitials" value="${team?.initials || ''}" maxlength="3">
            </div>
            <div class="form-group">
                <label for="teamMembers">Members (comma-separated):</label>
                <textarea id="teamMembers" required>${team?.members?.join(', ') || ''}</textarea>
            </div>
            <div class="form-group">
                <label for="teamIcon">Icon URL (optional):</label>
                <input type="url" id="teamIcon" value="${team?.icon || ''}">
            </div>
            <div class="form-actions">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeModalHandler()">CANCEL</button>
                <button type="submit" class="admin-btn admin-btn-primary">${isEdit ? 'UPDATE' : 'CREATE'}</button>
            </div>
        </form>
    `;

    showModal(title, content);

    document.getElementById('teamForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = {
            tournament_id: team?.tournament_id || tournamentId,
            name: document.getElementById('teamName').value,
            initials: document.getElementById('teamInitials').value,
            members: document.getElementById('teamMembers').value.split(',').map(m => m.trim()).filter(m => m),
            icon: document.getElementById('teamIcon').value || null
        };

        const endpoint = isEdit ? `/api/teams/${team.id}` : '/api/teams';
        const method = isEdit ? 'PATCH' : 'POST';
        const result = await apiCall(endpoint, method, formData);

        if (result) {
            closeModalHandler();
            loadTeams();
        }
    });
}

async function editTeam(teamId) {
    const team = teams.find(t => t.id === teamId);
    if (team) {
        showTeamForm(team);
    }
}

async function deleteTeam(teamId) {
    if (confirm('Are you sure you want to delete this team?')) {
        const result = await apiCall(`/api/teams/${teamId}`, 'DELETE');
        if (result) {
            loadTeams();
        }
    }
}

// Match functions
async function loadMatches() {
    const tournamentId = document.getElementById('matchTournamentSelect').value;
    if (!tournamentId) {
        document.getElementById('matchesList').innerHTML = '<p>Please select a tournament.</p>';
        return;
    }

    matches = await apiCall(`/api/tournaments/${tournamentId}/matches`) || [];
    displayMatches();
}

function displayMatches() {
    const container = document.getElementById('matchesList');

    if (matches.length === 0) {
        container.innerHTML = '<p>No matches found for this tournament.</p>';
        return;
    }

    container.innerHTML = matches.map(match => `
        <div class="admin-data-card">
            <h3>Match ${match.match_id || match.id}</h3>
            <p><strong>Teams:</strong> ${getTeamName(match.teamA)} vs ${getTeamName(match.teamB)}</p>
            <p><strong>Time:</strong> ${new Date(match.start_time).toLocaleString()}</p>
            <p><strong>Status:</strong> <span class="admin-status ${match.status}">${match.status.toUpperCase()}</span></p>
            ${match.status === 'completed' ? `
                <p><strong>Score:</strong> ${match.scoreA} - ${match.scoreB}</p>
                <p><strong>Winner:</strong> ${getTeamName(match.winner)}</p>
            ` : ''}
            <div class="admin-card-actions">
                <button class="admin-btn admin-btn-primary" onclick="editMatch('${match.id}')">EDIT</button>
                ${match.status === 'scheduled' ? `<button class="admin-btn admin-btn-secondary" onclick="completeMatch('${match.id}')">COMPLETE</button>` : ''}
                <button class="admin-btn admin-btn-danger" onclick="deleteMatch('${match.id}')">DELETE</button>
            </div>
        </div>
    `).join('');
}

function getTeamName(teamId) {
    const team = teams.find(t => t.id === teamId);
    return team ? team.name : 'Unknown Team';
}

function showMatchForm(match = null) {
    const tournamentId = document.getElementById('matchTournamentSelect').value;
    if (!tournamentId && !match) {
        alert('Please select a tournament first.');
        return;
    }

    const isEdit = match !== null;
    const title = isEdit ? 'Edit Match' : 'Add Match';

    // Get teams for the tournament
    const tournamentTeams = teams.filter(t => t.tournament_id === (match?.tournament_id || tournamentId));

    const content = `
        <form id="matchForm">
            <div class="form-group">
                <label for="matchId">Match ID (optional):</label>
                <input type="text" id="matchId" value="${match?.match_id || ''}">
            </div>
            <div class="form-group">
                <label for="teamA">Team A:</label>
                <select id="teamA" required>
                    <option value="">Select Team A</option>
                    ${tournamentTeams.map(team => `
                        <option value="${team.id}" ${match?.teamA === team.id ? 'selected' : ''}>${team.name}</option>
                    `).join('')}
                </select>
            </div>
            <div class="form-group">
                <label for="teamB">Team B:</label>
                <select id="teamB" required>
                    <option value="">Select Team B</option>
                    ${tournamentTeams.map(team => `
                        <option value="${team.id}" ${match?.teamB === team.id ? 'selected' : ''}>${team.name}</option>
                    `).join('')}
                </select>
            </div>
            <div class="form-group">
                <label for="startTime">Start Time:</label>
                <input type="datetime-local" id="startTime" value="${match?.start_time ? new Date(match.start_time).toISOString().slice(0, 16) : ''}" required>
            </div>
            <div class="form-actions">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeModalHandler()">CANCEL</button>
                <button type="submit" class="admin-btn admin-btn-primary">${isEdit ? 'UPDATE' : 'CREATE'}</button>
            </div>
        </form>
    `;

    showModal(title, content);

    document.getElementById('matchForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = {
            tournament_id: match?.tournament_id || tournamentId,
            teamA: document.getElementById('teamA').value,
            teamB: document.getElementById('teamB').value,
            start_time: document.getElementById('startTime').value,
            match_id: document.getElementById('matchId').value || null
        };

        const result = await apiCall('/api/matches', 'POST', formData);

        if (result) {
            closeModalHandler();
            loadMatches();
        }
    });
}

async function editMatch(matchId) {
    const match = matches.find(m => m.id === matchId);
    if (match) {
        showMatchForm(match);
    }
}

async function deleteMatch(matchId) {
    const match = matches.find(m => m.id === matchId);
    if (!match) return;

    if (confirm(`Are you sure you want to delete this match? This action cannot be undone.`)) {
        const result = await apiCall(`/api/matches/${matchId}`, 'DELETE');
        if (result) {
            alert('Match deleted successfully!');
            loadMatches();
        } else {
            alert('Failed to delete match. Check console for details.');
        }
    }
}

function completeMatch(matchId) {
    const match = matches.find(m => m.id === matchId);
    if (!match) return;

    const content = `
        <form id="completeMatchForm">
            <h3>Complete Match: ${getTeamName(match.teamA)} vs ${getTeamName(match.teamB)}</h3>
            <div class="form-group">
                <label for="scoreA">${getTeamName(match.teamA)} Score:</label>
                <input type="number" id="scoreA" min="0" required>
            </div>
            <div class="form-group">
                <label for="scoreB">${getTeamName(match.teamB)} Score:</label>
                <input type="number" id="scoreB" min="0" required>
            </div>
            <div class="form-group">
                <label for="winner">Winner:</label>
                <select id="winner" required>
                    <option value="">Select Winner</option>
                    <option value="${match.teamA}">${getTeamName(match.teamA)}</option>
                    <option value="${match.teamB}">${getTeamName(match.teamB)}</option>
                </select>
            </div>
            <div class="form-actions">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeModalHandler()">CANCEL</button>
                <button type="submit" class="admin-btn admin-btn-primary">COMPLETE MATCH</button>
            </div>
        </form>
    `;

    showModal('Complete Match', content);

    document.getElementById('completeMatchForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = {
            status: 'completed',
            scoreA: parseInt(document.getElementById('scoreA').value),
            scoreB: parseInt(document.getElementById('scoreB').value),
            winner: document.getElementById('winner').value
        };

        const result = await apiCall(`/api/matches/${matchId}`, 'PATCH', formData);

        if (result) {
            closeModalHandler();
            loadMatches();
        }
    });
}

// Bracket functions
async function loadBrackets() {
    const tournamentId = document.getElementById('bracketTournamentSelect').value;
    if (!tournamentId) {
        document.getElementById('bracketsList').innerHTML = '<p>Please select a tournament.</p>';
        return;
    }

    brackets = await apiCall(`/api/brackets/${tournamentId}`) || [];
    displayBrackets();
}

function displayBrackets() {
    const container = document.getElementById('bracketsList');

    if (brackets.length === 0) {
        container.innerHTML = '<p>No brackets found for this tournament.</p>';
        return;
    }

    container.innerHTML = brackets.map(bracket => `
        <div class="admin-data-card">
            <h3>Round ${bracket.round}</h3>
            <div class="matchups">
                ${bracket.matchups.map((matchup, index) => `
                    <div class="matchup" style="margin-bottom: 1rem; padding: 1rem; background: var(--dark-bg); border-radius: 6px;">
                        <strong>Matchup ${index + 1}:</strong><br>
                        ${matchup.teamA ? getTeamName(matchup.teamA) : 'TBD'} vs
                        ${matchup.teamB ? getTeamName(matchup.teamB) : 'TBD'}
                        ${matchup.winner ? `<br><span style="color: var(--success-green);">Winner: ${getTeamName(matchup.winner)}</span>` : ''}
                    </div>
                `).join('')}
            </div>
            <div class="admin-card-actions">
                <button class="admin-btn admin-btn-primary" onclick="editBracket('${bracket.id}')">EDIT</button>
                <button class="admin-btn admin-btn-danger" onclick="deleteBracket('${bracket.id}')">DELETE</button>
            </div>
        </div>
    `).join('');
}

function showBracketForm(bracket = null) {
    const tournamentId = document.getElementById('bracketTournamentSelect').value;
    if (!tournamentId && !bracket) {
        alert('Please select a tournament first.');
        return;
    }

    const isEdit = bracket !== null;
    const title = isEdit ? 'Edit Bracket' : 'Add Bracket';

    // Get teams for the tournament
    const tournamentTeams = teams.filter(t => t.tournament_id === (bracket?.tournament_id || tournamentId));

    const content = `
        <form id="bracketForm">
            <div class="form-group">
                <label for="round">Round:</label>
                <input type="number" id="round" value="${bracket?.round || 1}" min="1" required>
            </div>
            <div class="form-group">
                <label>Matchups:</label>
                <div id="matchupsContainer">
                    ${bracket?.matchups?.map((matchup, index) => `
                        <div class="matchup-form" data-index="${index}">
                            <h4>Matchup ${index + 1}</h4>
                            <select class="teamA">
                                <option value="">Team A (or TBD)</option>
                                ${tournamentTeams.map(team => `
                                    <option value="${team.id}" ${matchup.teamA === team.id ? 'selected' : ''}>${team.name}</option>
                                `).join('')}
                            </select>
                            <select class="teamB">
                                <option value="">Team B (or TBD)</option>
                                ${tournamentTeams.map(team => `
                                    <option value="${team.id}" ${matchup.teamB === team.id ? 'selected' : ''}>${team.name}</option>
                                `).join('')}
                            </select>
                            <select class="winner">
                                <option value="">Winner (optional)</option>
                                ${tournamentTeams.map(team => `
                                    <option value="${team.id}" ${matchup.winner === team.id ? 'selected' : ''}>${team.name}</option>
                                `).join('')}
                            </select>
                            <button type="button" onclick="removeMatchup(${index})">Remove</button>
                        </div>
                    `).join('') || ''}
                </div>
                <button type="button" onclick="addMatchup()">Add Matchup</button>
            </div>
            <div class="form-actions">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeModalHandler()">CANCEL</button>
                <button type="submit" class="admin-btn admin-btn-primary">${isEdit ? 'UPDATE' : 'CREATE'}</button>
            </div>
        </form>
    `;

    showModal(title, content);

    // Add initial matchup if creating new bracket
    if (!bracket) {
        addMatchup();
    }

    document.getElementById('bracketForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        const matchups = [];
        document.querySelectorAll('.matchup-form').forEach(form => {
            const teamA = form.querySelector('.teamA').value || null;
            const teamB = form.querySelector('.teamB').value || null;
            const winner = form.querySelector('.winner').value || null;

            matchups.push({ teamA, teamB, winner });
        });

        const formData = {
            tournament_id: bracket?.tournament_id || tournamentId,
            round: parseInt(document.getElementById('round').value),
            matchups
        };

        const result = await apiCall(`/api/brackets/${tournamentId}`, 'POST', formData);

        if (result) {
            closeModalHandler();
            loadBrackets();
        }
    });
}

function addMatchup() {
    const container = document.getElementById('matchupsContainer');
    const index = container.children.length;
    const tournamentId = document.getElementById('bracketTournamentSelect').value;
    const tournamentTeams = teams.filter(t => t.tournament_id === tournamentId);

    const matchupDiv = document.createElement('div');
    matchupDiv.className = 'matchup-form';
    matchupDiv.dataset.index = index;
    matchupDiv.innerHTML = `
        <h4>Matchup ${index + 1}</h4>
        <select class="teamA">
            <option value="">Team A (or TBD)</option>
            ${tournamentTeams.map(team => `<option value="${team.id}">${team.name}</option>`).join('')}
        </select>
        <select class="teamB">
            <option value="">Team B (or TBD)</option>
            ${tournamentTeams.map(team => `<option value="${team.id}">${team.name}</option>`).join('')}
        </select>
        <select class="winner">
            <option value="">Winner (optional)</option>
            ${tournamentTeams.map(team => `<option value="${team.id}">${team.name}</option>`).join('')}
        </select>
        <button type="button" onclick="removeMatchup(${index})">Remove</button>
    `;

    container.appendChild(matchupDiv);
}

function removeMatchup(index) {
    const matchup = document.querySelector(`[data-index="${index}"]`);
    if (matchup) {
        matchup.remove();
    }
}

async function editBracket(bracketId) {
    const bracket = brackets.find(b => b.id === bracketId);
    if (bracket) {
        showBracketForm(bracket);
    }
}

async function deleteBracket(bracketId) {
    const bracket = brackets.find(b => b.id === bracketId);
    if (!bracket) return;

    if (confirm(`Are you sure you want to delete this bracket? This action cannot be undone.`)) {
        const result = await apiCall(`/api/brackets/${bracketId}`, 'DELETE');
        if (result) {
            alert('Bracket deleted successfully!');
            loadBrackets();
        } else {
            alert('Failed to delete bracket. Check console for details.');
        }
    }
}
