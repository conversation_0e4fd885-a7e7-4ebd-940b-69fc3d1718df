/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: #0F1419;
    color: #FFFFFF;
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== VARIABLES ===== */
:root {
    --primary-red: #FF4655;
    --dark-bg: #0F1419;
    --card-bg: #1E2328;
    --border-color: #3C3C41;
    --text-primary: #FFFFFF;
    --text-secondary: #AAABAD;
    --accent-blue: #00D4FF;
    --success-green: #00F5A0;
    --warning-yellow: #FFCC02;
}

.screen {
    min-height: 100vh;
}

.hidden {
    display: none !important;
}

.accent {
    color: var(--primary-red);
}

/* ===== LOGIN SCREEN ===== */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #0F1419 0%, #1E2328 100%);
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23FF4655" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.login-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 2;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.login-form {
    margin-top: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-group input {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    background: var(--dark-bg);
    color: var(--text-primary);
    font-family: 'Rajdhani', sans-serif;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-red);
}

.login-btn {
    width: 100%;
    background: var(--primary-red);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    font-family: 'Rajdhani', sans-serif;
}

.login-btn:hover {
    background: #e63946;
    transform: translateY(-2px);
}

.error-message {
    color: var(--primary-red);
    margin-top: 1rem;
    text-align: center;
    font-weight: 500;
}

/* ===== NAVIGATION ===== */
.navbar {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid var(--border-color);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-subtitle {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.welcome-text {
    color: var(--text-secondary);
    font-weight: 500;
}

.logout-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.9rem;
    font-family: 'Rajdhani', sans-serif;
}

.logout-btn:hover {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: white;
}

/* ===== ADMIN CONTENT ===== */
.admin-content {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
    background: var(--dark-bg);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

/* ===== ADMIN TABS ===== */
.admin-tabs {
    display: flex;
    justify-content: center;
    gap: 0;
    margin-bottom: 3rem;
    background: var(--card-bg);
    border-radius: 8px;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
}

.admin-tab-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 1rem 2rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border-radius: 6px;
    font-family: 'Rajdhani', sans-serif;
    flex: 1;
}

.admin-tab-btn:hover {
    color: var(--primary-red);
    background: rgba(255, 70, 85, 0.1);
}

.admin-tab-btn.active {
    background: var(--primary-red);
    color: white;
}

.admin-tab-content {
    display: none;
}

.admin-tab-content.active {
    display: block;
}

/* ===== SECTION HEADERS ===== */
.admin-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--primary-red);
}

.section-actions,
.section-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* ===== ADMIN BUTTONS ===== */
.admin-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
    font-family: 'Rajdhani', sans-serif;
}

.admin-btn:hover {
    transform: translateY(-2px);
}

.admin-btn-primary {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: white;
}

.admin-btn-primary:hover {
    background: #e63946;
    border-color: #e63946;
}

.admin-btn-secondary {
    background: var(--accent-blue);
    border-color: var(--accent-blue);
    color: var(--dark-bg);
}

.admin-btn-secondary:hover {
    background: #00b8d4;
    border-color: #00b8d4;
}

.admin-btn-danger {
    background: transparent;
    border-color: var(--primary-red);
    color: var(--primary-red);
}

.admin-btn-danger:hover {
    background: var(--primary-red);
    color: white;
}

/* ===== ADMIN SELECTS ===== */
.admin-select {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.admin-select:focus {
    outline: none;
    border-color: var(--primary-red);
}

.admin-select option {
    background: var(--card-bg);
    color: var(--text-primary);
}

/* ===== ADMIN DATA GRID ===== */
.admin-data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.admin-data-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    transition: transform 0.3s ease, border-color 0.3s ease;
    position: relative;
    overflow: hidden;
}

.admin-data-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-red);
}

.admin-data-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
}

.admin-data-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.admin-data-card p {
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.admin-data-card strong {
    color: var(--text-primary);
    font-weight: 600;
}

.admin-card-actions {
    margin-top: 1.5rem;
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.admin-card-actions .admin-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* ===== STATUS BADGES ===== */
.admin-status {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 1px solid;
}

.admin-status.live {
    background: rgba(255, 70, 85, 0.2);
    color: var(--primary-red);
    border-color: var(--primary-red);
    animation: pulse 2s infinite;
}

.admin-status.upcoming {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border-color: var(--warning-yellow);
}

.admin-status.completed {
    background: rgba(0, 245, 160, 0.2);
    color: var(--success-green);
    border-color: var(--success-green);
}

.admin-status.scheduled {
    background: rgba(0, 212, 255, 0.2);
    color: var(--accent-blue);
    border-color: var(--accent-blue);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* ===== ADMIN MODAL ===== */
.admin-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.admin-modal-content {
    background: var(--card-bg);
    margin: 3% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.admin-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--dark-bg);
    border-radius: 12px 12px 0 0;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
}

.admin-modal-close {
    color: var(--text-secondary);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
}

.admin-modal-close:hover {
    color: var(--primary-red);
}

.admin-modal-body {
    padding: 2rem;
}

/* ===== MODAL FORM STYLES ===== */
.admin-modal .form-group {
    margin-bottom: 1.5rem;
}

.admin-modal .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.admin-modal .form-group input,
.admin-modal .form-group select,
.admin-modal .form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    background: var(--dark-bg);
    color: var(--text-primary);
    font-family: 'Rajdhani', sans-serif;
    transition: border-color 0.3s ease;
}

.admin-modal .form-group input:focus,
.admin-modal .form-group select:focus,
.admin-modal .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-red);
}

.admin-modal .form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.admin-modal .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* ===== HAMBURGER MENU ===== */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-actions {
        display: none;
    }

    .container {
        padding: 1rem;
    }

    .admin-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .admin-tab-btn {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }

    .admin-section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .section-actions,
    .section-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .admin-data-grid {
        grid-template-columns: 1fr;
    }

    .admin-modal-content {
        margin: 10% auto;
        width: 95%;
    }

    .admin-modal-header {
        padding: 1.5rem;
    }

    .admin-modal-body {
        padding: 1.5rem;
    }

    .admin-card-actions {
        justify-content: center;
    }

    .login-card {
        margin: 1rem;
        padding: 2rem;
    }

    .section-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .admin-tab-btn {
        padding: 0.5rem;
        font-size: 0.7rem;
    }

    .admin-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .admin-card-actions .admin-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.7rem;
    }
}
