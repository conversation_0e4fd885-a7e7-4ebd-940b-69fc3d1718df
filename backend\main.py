from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from motor.motor_asyncio import AsyncIOMotor<PERSON>lient
from datetime import datetime, timedelta, timezone
from jose import JWTError, jwt
from passlib.context import CryptContext
import pytz
import os
from dotenv import load_dotenv
from typing import Optional, List
from bson import ObjectId
import uvicorn
from contextlib import asynccontextmanager

# Load environment variables
load_dotenv()

# Lifespan event handler
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global client, database
    client = AsyncIOMotorClient(MONGODB_URL)
    database = client[DATABASE_NAME]
    print("Connected to MongoDB")
    yield
    # Shutdown
    if client:
        client.close()
        print("Disconnected from MongoDB")

# Initialize FastAPI app
app = FastAPI(title="Tournament Management API", version="1.0.0", lifespan=lifespan)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your frontend domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Configuration
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_HOURS = 24
ADMIN_USERNAME = os.getenv("ADMIN_USERNAME")
ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD")

# MongoDB connection
MONGODB_URL = os.getenv("MONGODB_URL")
DATABASE_NAME = os.getenv("DATABASE_NAME")

# Global variables
client = None
database = None

# IST timezone
IST = pytz.timezone('Asia/Kolkata')

# Pydantic models will be imported from models.py
from models import *
from utils import *

# Database connection is now handled by lifespan events above

# Authentication functions
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    return username

# Routes
@app.get("/")
async def root():
    return {"message": "Tournament Management API", "status": "running"}

@app.post("/api/login")
async def login(login_data: LoginRequest):
    print(f"Login attempt: {login_data.username}")
    if login_data.username != ADMIN_USERNAME or login_data.password != ADMIN_PASSWORD:
        print(f"Login failed for {login_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )

    access_token_expires = timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
    access_token = create_access_token(
        data={"sub": login_data.username}, expires_delta=access_token_expires
    )
    print(f"Login successful for {login_data.username}, token created")
    return {"access_token": access_token, "token_type": "bearer", "expires_in": ACCESS_TOKEN_EXPIRE_HOURS * 3600}

# Tournament routes
@app.post("/api/tournaments")
async def create_tournament(tournament: TournamentCreate, current_user: str = Depends(get_current_user)):
    print(f"Creating tournament: {tournament}")
    tournament_dict = tournament.model_dump()
    print(f"Tournament dict: {tournament_dict}")
    tournament_dict["created_at"] = datetime.now(timezone.utc)

    # Convert start_date to UTC if provided in IST
    if tournament_dict.get("start_date"):
        tournament_dict["start_date"] = convert_ist_to_utc(tournament_dict["start_date"])

    # Convert end_date to UTC if provided in IST
    if tournament_dict.get("end_date"):
        tournament_dict["end_date"] = convert_ist_to_utc(tournament_dict["end_date"])

    print(f"Final tournament dict before insert: {tournament_dict}")
    result = await database.tournaments.insert_one(tournament_dict)
    print(f"Insert result: {result.inserted_id}")
    created_tournament = await database.tournaments.find_one({"_id": result.inserted_id})
    print(f"Created tournament: {created_tournament}")
    return convert_tournament_response(created_tournament)

@app.get("/api/tournaments/live")
async def get_live_tournament():
    tournament = await database.tournaments.find_one({"status": "live"})
    if not tournament:
        return None
    return convert_tournament_response(tournament)

@app.get("/api/tournaments/completed")
async def get_completed_tournaments():
    tournaments = []
    async for tournament in database.tournaments.find({"status": "completed"}):
        tournaments.append(convert_tournament_response(tournament))
    return tournaments

@app.get("/api/tournaments/all")
async def get_all_tournaments():
    """Debug endpoint to get all tournaments regardless of status"""
    tournaments = []
    async for tournament in database.tournaments.find({}):
        tournaments.append(convert_tournament_response(tournament))
    print(f"Found {len(tournaments)} total tournaments in database")
    return tournaments

@app.patch("/api/tournaments/{tournament_id}")
async def update_tournament(tournament_id: str, tournament_update: TournamentCreate, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")

    update_data = tournament_update.model_dump(exclude_unset=True)

    # Convert dates to UTC if provided
    if update_data.get("start_date"):
        update_data["start_date"] = convert_ist_to_utc(update_data["start_date"])
    if update_data.get("end_date"):
        update_data["end_date"] = convert_ist_to_utc(update_data["end_date"])

    result = await database.tournaments.update_one(
        {"_id": ObjectId(tournament_id)},
        {"$set": update_data}
    )

    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Tournament not found")

    updated_tournament = await database.tournaments.find_one({"_id": ObjectId(tournament_id)})
    return convert_tournament_response(updated_tournament)

@app.patch("/api/tournaments/{tournament_id}/status")
async def update_tournament_status(tournament_id: str, status_update: StatusUpdate, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")
    
    # If setting to live, make sure no other tournament is live
    if status_update.status == "live":
        await database.tournaments.update_many(
            {"status": "live"},
            {"$set": {"status": "upcoming"}}
        )
    
    result = await database.tournaments.update_one(
        {"_id": ObjectId(tournament_id)},
        {"$set": {"status": status_update.status}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Tournament not found")
    
    updated_tournament = await database.tournaments.find_one({"_id": ObjectId(tournament_id)})
    return convert_tournament_response(updated_tournament)

@app.delete("/api/tournaments/{tournament_id}")
async def delete_tournament(tournament_id: str, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")

    # Delete all associated data first
    await database.teams.delete_many({"tournament_id": tournament_id})
    await database.matches.delete_many({"tournament_id": tournament_id})
    await database.brackets.delete_many({"tournament_id": tournament_id})

    # Delete the tournament
    result = await database.tournaments.delete_one({"_id": ObjectId(tournament_id)})

    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Tournament not found")

    return {"message": "Tournament and all associated data deleted successfully"}

# Team routes
@app.post("/api/teams")
async def create_team(team: TeamCreate, current_user: str = Depends(get_current_user)):
    team_dict = team.model_dump()

    # Generate initials if not provided
    if not team_dict.get("initials"):
        team_dict["initials"] = generate_initials(team_dict["name"])

    # Handle backward compatibility: if players list is empty but members exist,
    # convert members to basic player objects
    if not team_dict.get("players") and team_dict.get("members"):
        team_dict["players"] = [{"name": member, "role": None, "rank": None} for member in team_dict["members"]]

    # Ensure members list is populated for backward compatibility
    if team_dict.get("players") and not team_dict.get("members"):
        team_dict["members"] = [player["name"] for player in team_dict["players"]]

    result = await database.teams.insert_one(team_dict)
    created_team = await database.teams.find_one({"_id": result.inserted_id})
    return convert_team_response(created_team)

@app.get("/api/tournaments/{tournament_id}/teams")
async def get_tournament_teams(tournament_id: str):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")

    teams = []
    async for team in database.teams.find({"tournament_id": tournament_id}):
        teams.append(convert_team_response(team))
    return teams

@app.patch("/api/teams/{team_id}")
async def update_team(team_id: str, team_update: TeamUpdate, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(team_id):
        raise HTTPException(status_code=400, detail="Invalid team ID")

    update_data = {k: v for k, v in team_update.model_dump().items() if v is not None}

    # Update initials if name is being updated
    if "name" in update_data and "initials" not in update_data:
        update_data["initials"] = generate_initials(update_data["name"])

    # Handle backward compatibility for players/members
    if "players" in update_data and update_data["players"]:
        # Update members list for backward compatibility
        update_data["members"] = [player["name"] for player in update_data["players"]]
    elif "members" in update_data and update_data["members"]:
        # If only members are updated, create basic player objects
        update_data["players"] = [{"name": member, "role": None, "rank": None} for member in update_data["members"]]

    if not update_data:
        raise HTTPException(status_code=400, detail="No valid fields to update")

    result = await database.teams.update_one(
        {"_id": ObjectId(team_id)},
        {"$set": update_data}
    )

    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Team not found")

    updated_team = await database.teams.find_one({"_id": ObjectId(team_id)})
    return convert_team_response(updated_team)

@app.delete("/api/teams/{team_id}")
async def delete_team(team_id: str, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(team_id):
        raise HTTPException(status_code=400, detail="Invalid team ID")

    result = await database.teams.delete_one({"_id": ObjectId(team_id)})

    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Team not found")

    return {"message": "Team deleted successfully"}

# Match routes
@app.post("/api/matches")
async def create_match(match: MatchCreate, current_user: str = Depends(get_current_user)):
    match_dict = match.model_dump()

    # Convert start_time to UTC
    match_dict["start_time"] = convert_ist_to_utc(match_dict["start_time"])
    match_dict["status"] = "scheduled"

    result = await database.matches.insert_one(match_dict)
    created_match = await database.matches.find_one({"_id": result.inserted_id})
    return convert_match_response(created_match)

@app.get("/api/tournaments/{tournament_id}/matches")
async def get_tournament_matches(tournament_id: str):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")

    matches = []
    async for match in database.matches.find({"tournament_id": tournament_id}):
        matches.append(convert_match_response(match))
    return matches

@app.patch("/api/matches/{match_id}")
async def update_match(match_id: str, match_update: MatchUpdate, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(match_id):
        raise HTTPException(status_code=400, detail="Invalid match ID")

    update_data = {k: v for k, v in match_update.model_dump().items() if v is not None}

    if not update_data:
        raise HTTPException(status_code=400, detail="No valid fields to update")

    result = await database.matches.update_one(
        {"_id": ObjectId(match_id)},
        {"$set": update_data}
    )

    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Match not found")

    updated_match = await database.matches.find_one({"_id": ObjectId(match_id)})
    return convert_match_response(updated_match)

@app.delete("/api/matches/{match_id}")
async def delete_match(match_id: str, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(match_id):
        raise HTTPException(status_code=400, detail="Invalid match ID")

    result = await database.matches.delete_one({"_id": ObjectId(match_id)})

    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Match not found")

    return {"message": "Match deleted successfully"}

# Bracket routes
@app.post("/api/brackets/{tournament_id}")
async def create_bracket(tournament_id: str, bracket: BracketCreate, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")

    bracket_dict = bracket.model_dump()
    bracket_dict["tournament_id"] = tournament_id

    result = await database.brackets.insert_one(bracket_dict)
    created_bracket = await database.brackets.find_one({"_id": result.inserted_id})
    return convert_bracket_response(created_bracket)

@app.get("/api/brackets/{tournament_id}")
async def get_tournament_brackets(tournament_id: str):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")

    brackets = []
    async for bracket in database.brackets.find({"tournament_id": tournament_id}).sort("round", 1):
        brackets.append(convert_bracket_response(bracket))
    return brackets

@app.delete("/api/brackets/{bracket_id}")
async def delete_bracket(bracket_id: str, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(bracket_id):
        raise HTTPException(status_code=400, detail="Invalid bracket ID")

    result = await database.brackets.delete_one({"_id": ObjectId(bracket_id)})

    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Bracket not found")

    return {"message": "Bracket deleted successfully"}

# Ranks endpoint
@app.get("/api/ranks")
async def get_valorant_ranks():
    """Get all available Valorant ranks"""
    from models import VALORANT_RANKS
    return {"ranks": VALORANT_RANKS}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=int(os.getenv("PORT", 8000)), reload=True)
