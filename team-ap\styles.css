/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: #0F1419;
    color: #FFFFFF;
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== VARIABLES ===== */
:root {
    --primary-red: #FF4655;
    --dark-bg: #0F1419;
    --card-bg: #1E2328;
    --border-color: #3C3C41;
    --text-primary: #FFFFFF;
    --text-secondary: #AAABAD;
    --accent-blue: #00D4FF;
    --success-green: #00F5A0;
    --warning-yellow: #FFCC02;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.accent {
    color: var(--primary-red);
}

/* ===== LAYOUT ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-red);
}

/* ===== NAVIGATION ===== */
.navbar {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid var(--border-color);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-red);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-red);
}

/* ===== HERO SECTION ===== */
.hero {
    background: linear-gradient(135deg, #0F1419 0%, #1E2328 100%);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23FF4655" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #FF4655, #FFFFFF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 2rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-red);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* ===== SECTIONS ===== */
.upcoming-matches,
.recent-results,
.tournament-info {
    padding: 80px 0;
}

.upcoming-matches {
    background: var(--dark-bg);
}

.recent-results {
    background: var(--card-bg);
}

.tournament-info {
    background: var(--dark-bg);
}

/* ===== GRIDS ===== */
.matches-grid,
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.info-card {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    text-align: center;
}

.info-card h3 {
    color: var(--primary-red);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.info-card p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

/* ===== LOADING ===== */
.loading {
    text-align: center;
    font-size: 1.2rem;
    color: var(--accent-blue);
    padding: 2rem;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* ===== FOOTER ===== */
.footer {
    background: var(--card-bg);
    padding: 2rem 0;
    text-align: center;
    border-top: 1px solid var(--border-color);
}

.footer p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== MATCH CARDS ===== */
.match-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    transition: transform 0.3s ease, border-color 0.3s ease;
    position: relative;
    overflow: hidden;
}

.match-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-red);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.match-time {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.match-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-block;
    line-height: 1;
    vertical-align: middle;
}

.match-status.upcoming {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border: 1px solid var(--warning-yellow);
}

.match-status.live {
    background: rgba(255, 70, 85, 0.2);
    color: var(--primary-red);
    border: 1px solid var(--primary-red);
    animation: pulse 2s infinite;
}

.match-status.completed {
    background: rgba(0, 245, 160, 0.2);
    color: var(--success-green);
    border: 1px solid var(--success-green);
}

.match-teams {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    position: relative;
    gap: 0.75rem;
}

.team {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.team:last-child {
    justify-content: flex-end;
    text-align: right;
}

.team-logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--primary-red);
    flex-shrink: 0;
    aspect-ratio: 1;
}

.team-name {
    font-weight: 600;
    font-size: 1.1rem;
}

.vs-divider {
    color: var(--text-secondary);
    font-weight: 700;
    font-size: 1.2rem;
    margin: 0 1rem;
}

.match-score {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.score {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-red);
    text-align: center;
}

.match-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.match-map {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.match-round {
    color: var(--primary-red);
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== HAMBURGER MENU ===== */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* ===== PAGE HEADERS ===== */
.page-header {
    background: linear-gradient(135deg, #0F1419 0%, #1E2328 100%);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23FF4655" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.page-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.tournament-info-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== FILTER TABS ===== */
.filter-tabs {
    display: flex;
    justify-content: center;
    gap: 0;
    margin-bottom: 3rem;
    background: var(--card-bg);
    border-radius: 8px;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
}

.filter-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 1rem 2rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border-radius: 6px;
    font-family: 'Rajdhani', sans-serif;
    flex: 1;
}

.filter-btn:hover {
    color: var(--primary-red);
    background: rgba(255, 70, 85, 0.1);
}

.filter-btn.active {
    background: var(--primary-red);
    color: white;
}

/* ===== CONTENT SECTIONS ===== */
.schedule-content,
.brackets-content,
.history-content {
    padding: 80px 0;
    background: var(--dark-bg);
}

.brackets-container {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    overflow-x: auto;
}

.tournament-history-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* ===== MOBILE RESPONSIVENESS ===== */
@media screen and (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--card-bg);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.5rem;
    }

    .hero-stats {
        gap: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .matches-grid,
    .results-grid,
    .tournament-history-grid {
        grid-template-columns: 1fr;
    }

    .match-teams {
        flex-direction: column;
        gap: 1rem;
    }

    .vs-divider {
        margin: 0;
    }

    .team {
        justify-content: center;
    }

    .filter-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }
}

/* ===== SCHEDULE SPECIFIC STYLES ===== */
.schedule-match-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease, border-color 0.3s ease;
}

.schedule-match-card:hover {
    transform: translateY(-2px);
    border-color: var(--primary-red);
}

.schedule-match-card .match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.match-id {
    font-weight: 600;
    color: var(--text-secondary);
}

.schedule-match-card .match-teams {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.schedule-match-card .team {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.team-info {
    text-align: center;
}

.team-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.team-initials {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.team-score {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-red);
    min-width: 40px;
    text-align: center;
}

.vs-section {
    text-align: center;
    color: var(--text-secondary);
}

.vs-section .vs {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.vs-section .match-time {
    font-size: 0.9rem;
}

.match-result {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.winner-announcement {
    color: var(--success-green);
    font-weight: 600;
}

.winner-name {
    color: var(--primary-red);
}

.no-data {
    text-align: center;
    color: var(--text-secondary);
    font-size: 1.1rem;
    padding: 3rem;
}

/* ===== HISTORY PAGE STYLES ===== */
.history-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.history-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    transition: transform 0.3s ease, border-color 0.3s ease;
    position: relative;
    overflow: hidden;
}

.history-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--success-green);
}

.history-card:hover {
    transform: translateY(-5px);
    border-color: var(--success-green);
}

.history-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.history-card .tournament-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.tournament-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.tournament-info {
    margin-bottom: 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.info-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.info-value {
    color: var(--text-primary);
    font-weight: 600;
}

.info-value.prize {
    color: var(--warning-yellow);
}

.tournament-status.completed {
    color: var(--success-green);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.card-actions {
    text-align: center;
}

.view-details-btn {
    background: var(--primary-red);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    font-family: 'Rajdhani', sans-serif;
}

.view-details-btn:hover {
    background: #e63946;
    transform: translateY(-2px);
}

.no-history {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.no-history h2 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

/* ===== HISTORY MODAL STYLES ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.modal-content {
    background: var(--card-bg);
    border-radius: 12px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--dark-bg);
    border-radius: 12px 12px 0 0;
}

.modal-header h2 {
    color: var(--text-primary);
    margin: 0;
    font-size: 1.5rem;
}

.close-modal {
    color: var(--text-secondary);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
}

.close-modal:hover {
    color: var(--primary-red);
}

.modal-body {
    padding: 2rem;
}

.tournament-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-item h3 {
    color: var(--primary-red);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.summary-item p {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0;
}

.summary-item p.prize {
    color: var(--warning-yellow);
}

.summary-item p.winner {
    color: var(--success-green);
}

.teams-section {
    margin-top: 2rem;
}

.teams-section h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.team-card {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    text-align: center;
    transition: border-color 0.3s ease;
}

.team-card.champion {
    border-color: var(--warning-yellow);
    background: rgba(255, 204, 2, 0.1);
}

.team-card .team-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.team-card .team-initials {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.champion-badge {
    color: var(--warning-yellow);
    font-weight: 600;
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

/* ===== RANK ICONS AND PLAYER DISPLAY ===== */
.rank-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    vertical-align: middle;
}

.team-players {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    line-height: 1.4;
}

.match-card .team-players {
    font-size: 0.7rem;
    margin-top: 0.25rem;
}

.schedule-match-card .team-players {
    font-size: 0.7rem;
    margin-top: 0.25rem;
}

.bracket-team .team-players {
    font-size: 0.7rem;
    margin-top: 0.25rem;
}

.team-card .team-players {
    font-size: 0.75rem;
    margin-top: 0.5rem;
}
