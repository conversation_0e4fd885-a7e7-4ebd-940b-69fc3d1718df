// Configuration
const API_BASE_URL = 'http://localhost:8000'; // Change this to your deployed backend URL

// Global state
let currentTournament = null;
let teams = [];
let brackets = [];

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadData();
});

// Setup event listeners
function setupEventListeners() {
    // Mobile menu toggle
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }
}

// API helper function
async function apiCall(endpoint) {
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        
        if (response.ok) {
            return await response.json();
        } else {
            console.error(`API call failed: ${response.status} ${response.statusText}`);
            return null;
        }
    } catch (error) {
        console.error('API call error:', error);
        return null;
    }
}

// Load all data
async function loadData() {
    await loadCurrentTournament();
    if (currentTournament) {
        await loadTeams();
        await loadBrackets();
        displayTournamentInfo();
        displayBrackets();
    } else {
        await loadMostRecentTournament();
    }
}

// Load current live tournament
async function loadCurrentTournament() {
    currentTournament = await apiCall('/api/tournaments/live');
}

// Load most recent completed tournament if no live tournament
async function loadMostRecentTournament() {
    const completedTournaments = await apiCall('/api/tournaments/completed');
    if (completedTournaments && completedTournaments.length > 0) {
        currentTournament = completedTournaments.sort((a, b) => 
            new Date(b.created_at) - new Date(a.created_at)
        )[0];
        
        await loadTeams();
        await loadBrackets();
        displayTournamentInfo();
        displayBrackets();
    } else {
        displayNoTournament();
    }
}

// Load teams for current tournament
async function loadTeams() {
    if (currentTournament) {
        teams = await apiCall(`/api/tournaments/${currentTournament.id}/teams`) || [];
    }
}

// Load brackets for current tournament
async function loadBrackets() {
    if (currentTournament) {
        brackets = await apiCall(`/api/brackets/${currentTournament.id}`) || [];
        // Sort brackets by round
        brackets.sort((a, b) => a.round - b.round);
    }
}

// Display tournament information
function displayTournamentInfo() {
    const tournamentInfoElement = document.getElementById('tournamentInfo');
    
    if (!currentTournament) {
        displayNoTournament();
        return;
    }
    
    const statusText = currentTournament.status === 'live' ? 'LIVE NOW' : 
                      currentTournament.status === 'upcoming' ? 'UPCOMING' : 'COMPLETED';
    
    tournamentInfoElement.innerHTML = `
        <div class="tournament-info-content">
            <h2 class="tournament-name">${currentTournament.name}</h2>
            <div class="tournament-details">
                <span class="tournament-type">${currentTournament.type}</span>
                <span class="tournament-status ${currentTournament.status}">${statusText}</span>
                ${currentTournament.prize_pool ? `<span class="tournament-prize">₹${currentTournament.prize_pool.toLocaleString()}</span>` : ''}
            </div>
        </div>
    `;
}

// Display brackets
// Helper function to get rank image path
function getRankImagePath(filename) {
    return filename ? `rank_png/${filename}` : '';
}

// Helper function to create player list with ranks
function createPlayerList(team, maxPlayers = 2) {
    if (!team) return '';

    let players = [];
    if (team.players && team.players.length > 0) {
        players = team.players;
    } else if (team.members && team.members.length > 0) {
        players = team.members.map(member => ({ name: member, rank: null }));
    }

    if (players.length === 0) return '';

    const displayPlayers = players.slice(0, maxPlayers);
    const remainingCount = players.length - maxPlayers;

    let playerList = displayPlayers.map(player => {
        const rankImg = player.rank ?
            `<img src="${getRankImagePath(player.rank)}" alt="Rank" class="rank-icon">` : '';
        return `${rankImg}${player.name}`;
    }).join(', ');

    if (remainingCount > 0) {
        playerList += ` +${remainingCount}`;
    }

    return playerList;
}

function displayBrackets() {
    const bracketsContainer = document.getElementById('bracketsContainer');

    if (!brackets || brackets.length === 0) {
        bracketsContainer.innerHTML = `
            <div class="no-brackets">
                <h2>No Brackets Available</h2>
                <p>Brackets will be displayed once the tournament structure is set up.</p>
            </div>
        `;
        return;
    }

    bracketsContainer.innerHTML = `
        <div class="brackets-wrapper">
            ${brackets.map(bracket => `
                <div class="bracket-round">
                    <h3 class="round-title">Round ${bracket.round}</h3>
                    <div class="matchups-container">
                        ${bracket.matchups.map((matchup, index) => {
                            const teamA = matchup.teamA ? getTeamById(matchup.teamA) : null;
                            const teamB = matchup.teamB ? getTeamById(matchup.teamB) : null;

                            return `
                                <div class="bracket-matchup">
                                    <div class="matchup-header">Matchup ${index + 1}</div>
                                    <div class="matchup-teams">
                                        <div class="bracket-team ${matchup.winner === matchup.teamA ? 'winner' : ''}">
                                            <div class="team-name">${teamA?.name || 'TBD'}</div>
                                            <div class="team-initials">${teamA?.initials || '??'}</div>
                                            ${teamA ? `<div class="team-players">${createPlayerList(teamA)}</div>` : ''}
                                        </div>
                                        <div class="vs-divider">VS</div>
                                        <div class="bracket-team ${matchup.winner === matchup.teamB ? 'winner' : ''}">
                                            <div class="team-name">${teamB?.name || 'TBD'}</div>
                                            <div class="team-initials">${teamB?.initials || '??'}</div>
                                            ${teamB ? `<div class="team-players">${createPlayerList(teamB)}</div>` : ''}
                                        </div>
                                    </div>
                                    ${matchup.winner ? `
                                        <div class="matchup-winner">
                                            🏆 Winner: ${getTeamById(matchup.winner)?.name || 'Unknown Team'}
                                        </div>
                                    ` : ''}
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// Display no tournament message
function displayNoTournament() {
    const tournamentInfoElement = document.getElementById('tournamentInfo');
    const bracketsContainer = document.getElementById('bracketsContainer');
    
    const noTournamentMessage = `
        <div class="no-tournament">
            <h2>No Tournament Currently Available</h2>
            <p>Check back soon for upcoming tournaments!</p>
        </div>
    `;
    
    if (tournamentInfoElement) {
        tournamentInfoElement.innerHTML = noTournamentMessage;
    }
    
    if (bracketsContainer) {
        bracketsContainer.innerHTML = `
            <div class="no-brackets">
                <h2>No Tournament is Live Currently</h2>
                <p>Brackets will be available when a tournament is active.</p>
            </div>
        `;
    }
}

// Helper function to get team by ID
function getTeamById(teamId) {
    return teams.find(team => team.id === teamId);
}
